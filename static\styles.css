:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #c0392b;
    --background-color: #f8f9fa;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

body {
    background: var(--background-color);
    font-family: 'Poppins', sans-serif;
    margin: 0;
    padding: 0;
}

.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--card-shadow);
    animation: fadeIn 1s ease-out;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.prediction-form {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 2rem;
    margin: 2rem auto;
    max-width: 800px;
    transition: transform var(--transition-speed);
}

.prediction-form:hover {
    transform: translateY(-5px);
}

.input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
    color: #495057;
    font-weight: 500;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.8rem;
    transition: all var(--transition-speed);
}

.form-control:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.btn-predict {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all var(--transition-speed);
    position: relative;
    overflow: hidden;
}

.btn-predict:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

.btn-predict:active {
    transform: translateY(0);
}

.result-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 2rem;
    margin: 2rem auto 8rem auto; /* Increased bottom margin to ensure more space for popup */
    max-width: 600px;
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-speed);
}

.result-card.visible {
    opacity: 1;
    transform: translateY(0);
}

.risk-indicator {
    font-size: 2rem;
    font-weight: bold;
    text-align: center;
    margin: 1rem 0;
    padding: 1rem;
    border-radius: 10px;
    transition: all var(--transition-speed);
    position: relative;
    overflow: hidden;
    padding: 1.5rem;
    border-radius: 15px;
    animation: fadeInScale 0.5s ease-out;
}

.risk-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

.low-risk {
    color: var(--success-color);
    background: rgba(39, 174, 96, 0.1);
}

.medium-risk {
    color: var(--warning-color);
    background: rgba(243, 156, 18, 0.1);
}

.high-risk {
    color: var(--danger-color);
    background: rgba(192, 57, 43, 0.1);
}

.language-switch {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    gap: 10px;
}

.language-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    transition: all var(--transition-speed);
    cursor: pointer;
}

.language-btn:hover,
.language-btn.active {
    background: var(--primary-color);
    color: white;
}

.medical-disclaimer {
    background: rgba(243, 156, 18, 0.1);
    border-left: 4px solid var(--warning-color);
    padding: 1rem;
    margin: 2rem auto;
    max-width: 800px;
    border-radius: 10px;
}

.login-form {
    background: white;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    padding: 2.5rem;
    margin: 2rem auto;
    max-width: 500px;
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-form .form-control {
    font-size: 1.1rem;
    padding: 1rem;
    text-align: left;
}

.login-form .btn-predict {
    margin-top: 1.5rem;
    font-size: 1.1rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner animation */
.spinner-border {
    display: none;
    margin-left: 0.5rem;
    animation: spinner 0.8s linear infinite;
    position: relative;
    margin-left: 10px;
    width: 20px;
    height: 20px;
}

.spinner-border::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 3px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Unit labels */
.input-group-text {
    background: var(--background-color);
    border-color: #e9ecef;
    color: var(--primary-color);
    font-weight: 500;
    border-radius: 0 10px 10px 0;
}

/* Image styles and effects */
.site-logo {
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
    animation: fadeInRotate 1s ease-out;
}

.site-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.medical-icon {
    width: 64px;
    height: 64px;
    margin: 10px;
    transition: transform 0.3s ease;
}

.medical-icon:hover {
    transform: scale(1.1);
}

.header-bg {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.95), rgba(52, 152, 219, 0.95));
}

.header-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../static/images/medical-bg.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.15;
    z-index: -1;
}

.result-image {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
    animation: pulseEffect 2s infinite;
}

@keyframes fadeInRotate {
    from {
        opacity: 0;
        transform: rotate(-10deg);
    }
    to {
        opacity: 1;
        transform: rotate(0);
    }
}

@keyframes pulseEffect {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.feature-image {
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    max-width: 100%;
    margin: 15px 0;
}

.feature-image:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.footer {
    position: relative;
    padding: 20px 0;
    margin-top: 150px; /* Increased margin to ensure it's at the very bottom */
    background: rgba(255, 255, 255, 0.9);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 1; /* Very low z-index to ensure it doesn't interfere with popups */
    font-family: 'Arial', sans-serif;
    font-size: 14px;
    color: var(--primary-color);
}

/* Footer hover effect removed as it's now a static element */

/* Responsive Design */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2rem;
    }

    .prediction-form {
        margin: 1rem;
        padding: 1.5rem;
    }

    .btn-predict {
        width: 100%;
    }

    .language-switch {
        top: 10px;
        right: 10px;
    }
}

/* Model Comparison Styles */
.model-comparison {
    padding: 2rem 0;
    animation: fadeIn 0.8s ease-out;
}

.comparison-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    height: 100%;
    transition: transform var(--transition-speed);
}

.comparison-card:hover {
    transform: translateY(-5px);
}

.comparison-card h4 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
    font-weight: 600;
}

.metrics {
    background: var(--background-color);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.metric-item:last-child {
    margin-bottom: 0;
}

.metric-label {
    color: var(--primary-color);
    font-weight: 500;
}

.metric-value {
    font-weight: 600;
    color: var (--secondary-color);
}

.strengths, .weaknesses {
    margin-top: 1rem;
}

.strengths h5, .weaknesses h5 {
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.strengths p, .weaknesses p {
    font-size: 0.9rem;
    margin-bottom: 0;
    color: #666;
}

.model-recommendation {
    background: rgba(52, 152, 219, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.model-recommendation h5 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.model-recommendation p {
    color: #666;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .comparison-card {
        margin-bottom: 1rem;
    }

    .metrics {
        padding: 0.8rem;
    }

    .metric-item {
        font-size: 0.8rem;
    }
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    animation: modalSlideIn 0.4s ease-out;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 20px 20px 0 0;
    padding: 1.5rem;
}

.modal-title {
    font-weight: 600;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.category-item {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border-left: 4px solid var(--secondary-color);
}

.category-item:hover {
    transform: translateX(5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.category-item h6 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.category-item h6::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background: var(--secondary-color);
    border-radius: 50%;
    margin-right: 10px;
}

/* Enhanced Button Styles */
.btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: -1;
}

.btn:hover::after {
    transform: translateX(0);
}

.btn-outline-info {
    border-width: 2px;
    font-weight: 500;
    padding: 0.4rem 0.8rem;
    transition: all 0.3s ease;
}

.btn-outline-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
}

/* Enhanced Form Controls */
.form-control, .form-select {
    border-width: 2px;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.15);
}

/* Glassmorphism Effect */
.smoking-categories {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 1rem;
}

/* New Animations */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-60px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes floatingEffect {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
    }
}

/* Enhanced Card Shadows */
.prediction-form, .result-card, .comparison-card {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

/* Risk Level Indicator Enhancement */
.risk-indicator {
    position: relative;
    overflow: hidden;
    padding: 1.5rem;
    border-radius: 15px;
    animation: fadeInScale 0.5s ease-out;
}

.risk-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .modal-content {
        margin: 10px;
    }

    .category-item {
        padding: 1rem;
    }

    .modal-body {
        padding: 1.5rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .modal-content {
        background: #1a1a1a;
        color: #ffffff;
    }

    .category-item {
        background: #242424;
        border-color: var(--secondary-color);
    }

    .modal-header {
        background: linear-gradient(135deg, #1a1a1a, var(--secondary-color));
    }

    .text-muted {
        color: #a0a0a0 !important;
    }
}

/* Feature Contribution Button Styles */
.btn-feature-contribution {
    margin-top: 1rem;
    opacity: 0.7;
    transition: all var(--transition-speed);
}

.btn-feature-contribution:not(:disabled) {
    opacity: 1;
}

.btn-feature-contribution:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3);
}

/* Modal enhancements for feature plots */
.modal-lg {
    max-width: 900px;  /* Larger modal for better plot visibility */
}

.feature-plot {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin: 1rem 0;
    transition: transform var(--transition-speed);
}

.feature-plot:hover {
    transform: scale(1.02);
}

/* Inline Feature Importance Styles */
.feature-bars-container {
    max-height: 600px;
    overflow-y: auto;
    padding-right: 8px;
}

.feature-bars-container::-webkit-scrollbar {
    width: 6px;
}

.feature-bars-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.feature-bars-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.feature-bars-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.feature-bar-item {
    opacity: 0;
    transform: translateX(-20px);
    animation: slideInLeft 0.6s ease-out forwards;
    transition: all 0.3s ease;
    padding: 12px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-bar-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.8);
}

@keyframes slideInLeft {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
}

.progress {
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.progress-bar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), transparent);
    border-radius: inherit;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* Enhanced badge styles */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* SHAP direction indicators */
.shap-indicator {
    font-size: 1.2rem;
    font-weight: bold;
    margin-left: 0.5rem;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.feature-bar-item:hover .shap-indicator {
    opacity: 1;
}

/* Responsive design */
@media (max-width: 768px) {
    .feature-bar-item {
        padding: 8px;
        margin-bottom: 1rem;
    }

    .progress {
        height: 24px !important;
    }

    .badge {
        font-size: 0.7rem;
        padding: 0.4rem 0.6rem;
    }
}

/* Dark mode support for feature importance */
@media (prefers-color-scheme: dark) {
    .feature-bar-item {
        background: rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.1);
        color: #ffffff;
    }

    .feature-bar-item:hover {
        background: rgba(0, 0, 0, 0.5);
    }

    .progress {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
    }
}

.smoking-category-item {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all var(--transition-speed);
    border-left: 4px solid var(--secondary-color);
}

.smoking-category-item:hover {
    transform: translateX(5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.category-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.category-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    color: var(--secondary-color);
}

.category-content {
    padding-left: 2.5rem;
}

.section-title {
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.definition-section {
    margin-bottom: 1rem;
    padding: 0.8rem;
    background: rgba(52, 152, 219, 0.05);
    border-radius: 8px;
}

.explanation-section {
    padding: 0.8rem;
    background: rgba(52, 152, 219, 0.02);
    border-radius: 8px;
}

.smoking-categories {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 1rem;
}

.smoking-categories::-webkit-scrollbar {
    width: 8px;
}

.smoking-categories::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.smoking-categories::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

.smoking-categories::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

@media (max-width: 768px) {
    .smoking-category-item {
        padding: 1.2rem;
        margin-bottom: 1rem;
    }

    .category-content {
        padding-left: 1rem;
    }

    .definition-section,
    .explanation-section {
        padding: 0.6rem;
    }
}

.health-advice {
    background: rgba(52, 152, 219, 0.05);
    border-left: 4px solid var(--secondary-color);
    margin-top: 2rem;
    animation: slideIn 0.5s ease-out;
}

.health-advice h5 {
    color: var(--primary-color);
    font-weight: 600;
}

.advice-content {
    color: #555;
    line-height: 1.6;
    font-size: 0.95rem;
    text-align: left;
    white-space: pre-line;
}

.advice-content.dark-mode {
    color: #e0e0e0;
}

@media (max-width: 768px) {
    .health-advice {
        margin: 1.5rem 0;
        padding: 1rem;
    }

    .advice-content {
        font-size: 0.9rem;
    }
}

.health-advice-icon {
    position: fixed;
    bottom: 80px;
    right: 20px;
    background: white;
    border-radius: 50%;
    padding: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.health-advice-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.health-advice-popup {
    display: none;
    position: absolute;
    top: calc(100% + 10px); /* Position 10px below the button */
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    transition: all 0.3s ease;
}

.health-advice-popup.show {
    display: block;
    opacity: 1;
    transform: translateX(-50%);
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
    background: var(--secondary-color);
    color: white;
    border-radius: 15px 15px 0 0;
}

.popup-header .btn-close {
    filter: brightness(0) invert(1);
}

.popup-content {
    padding: 15px;
    max-height: 400px; /* Further increased to show more content */
    overflow-y: auto;
}

.chat-message {
    background: rgba(52, 152, 219, 0.1);
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 10px;
    line-height: 1.4;
    font-size: 0.95rem;
    color: var(--primary-color);
}

@media (prefers-color-scheme: dark) {
    .health-advice-popup {
        background: #1a1a1a;
    }

    .popup-header {
        background: var(--primary-color);
    }

    .chat-message {
        background: rgba(52, 152, 219, 0.15);
        color: #e0e0e0;
    }
}

.divider {
    border-top: 1px solid rgba(52, 152, 219, 0.2);
    margin: 10px 0;
}

.advice-en {
    color: var(--primary-color);
}

.advice-ru {
    color: var(--primary-color);
    opacity: 0.9;
}

@media (prefers-color-scheme: dark) {
    .divider {
        border-color: rgba(255, 255, 255, 0.1);
    }

    .advice-en,
    .advice-ru {
        color: #e0e0e0;
    }

    .advice-ru {
        opacity: 0.85;
    }
}

/* BMI Calculator Styles */
.bmi-calculator {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 15px;
    margin-top: 10px;
    box-shadow: var(--card-shadow);
    animation: slideIn 0.3s ease-out;
}

#calculatedBmi {
    color: var(--primary-color);
    font-weight: 600;
}

#bmiCategory {
    color: var(--secondary-color);
}

/* BMI calculator button styles */
#showBmiCalc {
    font-size: 0.9rem;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    transition: all var(--transition-speed);
}

#showBmiCalc:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

#bmiCalculator input {
    border-color: #e9ecef;
    transition: all var(--transition-speed);
}

#bmiCalculator input:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.15);
}

#bmiCalculator .input-group-text,
#bmiCalculator .form-select {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: var(--primary-color);
}
