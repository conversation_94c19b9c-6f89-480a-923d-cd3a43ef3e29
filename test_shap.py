#!/usr/bin/env python3

print("Testing SHAP import...")
try:
    import shap
    print("✓ SHAP imported successfully")
except Exception as e:
    print(f"✗ SHAP import failed: {e}")

print("Testing other imports...")
try:
    import numpy as np
    import pandas as pd
    from sklearn.ensemble import RandomForestClassifier
    print("✓ All imports successful")
except Exception as e:
    print(f"✗ Import failed: {e}")

print("Testing model loading...")
try:
    import pickle
    import os
    
    model_path = os.path.join('models', 'SR_Random_Forest.pkl')
    if os.path.exists(model_path):
        with open(model_path, 'rb') as f:
            model = pickle.load(f)
        print(f"✓ Model loaded successfully: {type(model)}")
    else:
        print(f"✗ Model file not found: {model_path}")
except Exception as e:
    print(f"✗ Model loading failed: {e}")

print("Test completed!")
